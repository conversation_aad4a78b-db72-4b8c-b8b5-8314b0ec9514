#coding=UTF-8
from hashlib import md5
import os
import re
import time
from urllib import parse
import requests
import json

class Kugou_music():
    def __init__(self, address="http://localhost:3001"):
        self.kugou = requests.session()
        self.headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                                      'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36'}
        self.address = address
        try:
            with open('kugou.txt', 'r') as f:
                pass
            self.load_cookie()
        except:
            print("请先登录酷狗账号")
            self.log_in()
    def load_cookie(self):
        with open('kugou.txt') as f:
            cookie = json.loads(f.read())
            self.kugou.cookies.update(cookie)

    def save_cookie(self):
        with open('kugou.txt','w') as f:
            cookie=json.dumps(self.kugou.cookies.get_dict())
            f.write(cookie)
    
    def search_music(self, keywords, limit=10):
        """
        搜索音乐，支持 "歌名" 或 "歌手 - 歌名" 格式
        返回: (hash, name, artist) 或 (0, "", "") 表示未找到
        增加匹配度排序功能，使用与download_music相同的搜索方式
        """
        try:
            # 使用与download_music相同的搜索方式
            search_url = self.MD5Encrypt(keywords)
            search_text = self.get_html(search_url)

            if search_text == '请求异常':
                return 0, "", ""

            hash_list = self.parse_text(search_text[12:-2])

            if not hash_list:
                return 0, "", ""

            # 计算匹配度并排序
            scored_songs = []
            for hash_item in hash_list[:limit]:  # 限制处理数量
                # hash_item格式: [file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name]
                if len(hash_item) >= 6:
                    song_name = hash_item[4] if hash_item[4] else ""
                    artist_name = hash_item[5] if hash_item[5] else ""
                    file_hash = hash_item[2] if hash_item[2] else hash_item[0]  # 优先使用SQ，然后普通

                    if file_hash and song_name:
                        # 计算匹配度分数
                        score = self._calculate_match_score(keywords, song_name, artist_name)
                        scored_songs.append((score, file_hash, song_name, artist_name))

            if not scored_songs:
                return 0, "", ""

            # 按匹配度降序排序
            scored_songs.sort(key=lambda x: x[0], reverse=True)

            # 返回匹配度最高的歌曲
            best_match = scored_songs[0]
            return best_match[1], best_match[2], best_match[3]  # hash, song_name, artist_name

        except Exception as e:
            print(f"搜索音乐异常: {e}")
            return 0, "", ""

    def _calculate_match_score(self, keywords, song_name, artist_name):
        """
        计算搜索关键词与歌曲的匹配度分数
        支持多种搜索格式：
        1. 仅歌名：稻香
        2. 歌手-歌名：周杰伦-稻香 或 周杰伦 - 稻香
        3. 歌名 歌手：稻香 周杰伦
        4. 复杂格式：蓝光乐队 - 轻舟(dj阿卓版)
        """
        score = 0
        keywords_lower = keywords.lower().strip()
        song_name_lower = song_name.lower().strip()
        artist_name_lower = artist_name.lower().strip()

        # 预处理：统一分隔符
        keywords_normalized = keywords_lower.replace('、', '-').replace('，', '-').replace(',', '-')

        # 完全匹配歌名得最高分
        if keywords_lower == song_name_lower:
            score += 100
        elif keywords_lower in song_name_lower:
            score += 80
        elif song_name_lower in keywords_lower:
            score += 70

        # 解析搜索关键词的不同格式
        search_artist = ""
        search_song = ""

        # 格式1: 歌手-歌名 或 歌手 - 歌名
        if ' - ' in keywords_normalized or '-' in keywords_normalized:
            parts = keywords_normalized.replace(' - ', '-').split('-', 1)  # 只分割第一个-
            if len(parts) >= 2:
                search_artist = parts[0].strip()
                search_song = parts[1].strip()
        # 格式2: 歌名 歌手 (空格分隔，且歌手在后面)
        elif ' ' in keywords_lower and len(keywords_lower.split()) == 2:
            parts = keywords_lower.split()
            # 尝试判断哪个是歌手哪个是歌名
            if parts[1] in artist_name_lower or artist_name_lower in parts[1]:
                search_song = parts[0]
                search_artist = parts[1]
            elif parts[0] in artist_name_lower or artist_name_lower in parts[0]:
                search_artist = parts[0]
                search_song = parts[1]
            else:
                # 默认第一个是歌名，第二个是歌手
                search_song = parts[0]
                search_artist = parts[1]
        else:
            # 格式3: 仅歌名
            search_song = keywords_lower

        # 歌手匹配评分
        if search_artist:
            if search_artist == artist_name_lower:
                score += 50
            elif search_artist in artist_name_lower or artist_name_lower in search_artist:
                score += 30
            # 处理多歌手情况（如"蓝光乐队、孟西"）
            elif '、' in artist_name_lower or ',' in artist_name_lower:
                artist_parts = artist_name_lower.replace('、', ',').split(',')
                for artist_part in artist_parts:
                    artist_part = artist_part.strip()
                    if search_artist == artist_part or search_artist in artist_part:
                        score += 35
                        break

        # 歌名匹配评分
        if search_song:
            if search_song == song_name_lower:
                score += 50
            elif search_song in song_name_lower:
                score += 40
            elif song_name_lower in search_song:
                score += 35
            # 处理括号内容（如"轻舟(dj阿卓版)"）
            elif '(' in song_name_lower or '（' in song_name_lower:
                # 提取括号前的主要歌名
                main_song = re.sub(r'[（(].*?[）)]', '', song_name_lower).strip()
                if search_song == main_song or search_song in main_song:
                    score += 30

        # 如果没有明确的歌手信息，检查关键词是否匹配歌手
        if not search_artist and keywords_lower in artist_name_lower:
            score += 20

        # 字符相似度加分
        common_chars = set(keywords_lower) & set(song_name_lower)
        score += len(common_chars) * 2

        # 如果歌手和歌名都匹配，额外加分
        if search_artist and search_song:
            if (search_artist in artist_name_lower or artist_name_lower in search_artist) and \
               (search_song in song_name_lower or song_name_lower in search_song):
                score += 20

        return score

    def get_song_filename(self, keywords):
        """
        获取歌曲的标准化文件名（不包含扩展名）
        返回: (filename, full_song_name) 或 (None, None) 表示未找到
        """
        song_hash, song_name, artist_name = self.search_music(keywords)
        if song_hash == 0:
            return None, None

        # 清理文件名中的特殊字符
        clean_song_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', song_name).rstrip('. ').replace('(', '_').replace(')', '').replace(' ', '')
        clean_artist_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', artist_name).rstrip('. ').replace('(', '_').replace(')', '_').replace(' ', '')

        # 生成文件名
        if clean_artist_name:
            filename = f"{clean_artist_name}-{clean_song_name}"
            full_song_name = f"{artist_name}-{song_name}"
        else:
            filename = clean_song_name
            full_song_name = song_name

        return filename, full_song_name

    def search_download_music(self, keywords, level="exhigh"):
        """
        通过搜索下载音乐（备用方法，使用hash获取下载链接）
        """
        song_hash, name, artist = self.search_music(keywords)
        if song_hash == 0:
            raise Exception(f"未找到歌曲: {keywords}")

        if not os.path.exists('input'):
            os.mkdir('input')

        # 使用hash获取歌曲URL
        response = self.kugou.get(self.address + f"/song/url?hash={song_hash}").text
        song_data = json.loads(response)

        # 尝试获取下载URL
        download_url = None
        if 'backupUrl' in song_data and song_data['backupUrl']:
            download_url = song_data['backupUrl'][0]
        elif 'url' in song_data and song_data['url']:
            download_url = song_data['url'][0] if isinstance(song_data['url'], list) else song_data['url']
        elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
            download_url = song_data['data']['url']

        if not download_url:
            raise Exception("无法获取歌曲下载链接")

        song = self.kugou.get(download_url)

        # 确定文件扩展名
        if download_url.endswith('.mp3'):
            suffix = 'mp3'
        elif download_url.endswith('.flac'):
            suffix = 'flac'
        else:
            suffix = 'mp3'  # 默认mp3

        # 生成包含歌手信息的文件名
        clean_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', name).rstrip('. ')
        clean_artist = re.sub(r'[\[\]<>:"/\\|?*]', '_', artist).rstrip('. ') if artist else ''

        if clean_artist:
            filename = f"{clean_artist}-{clean_name}.{suffix}"
        else:
            filename = f"{clean_name}.{suffix}"

        file_path = os.path.join('input', filename)

        with open(file_path, 'wb') as f:
            f.write(song.content)

        return f"{artist}-{name}" if artist else name, file_path

    def download_music(self, keywords, level="exhigh"):
        """
        下载音乐主方法
        keywords: 搜索关键词，支持 "歌名" 或 "歌手 - 歌名" 格式
        """
        print(f"搜索歌曲: {keywords}")

        # 通过酷狗搜索API获取歌曲信息
        search_url = self.MD5Encrypt(keywords)
        search_text = self.get_html(search_url)
        hash_list = self.parse_text(search_text[12:-2])

        if not hash_list:
            print(f"未找到歌曲: {keywords}")
            # raise Exception(f"未找到歌曲: {keywords}")

        if not os.path.exists('input'):
            os.mkdir('input')

        # 尝试获取歌曲URL和详细信息
        song_url = None
        song_info = None
        used_hash_info = None

        for i, hash_item in enumerate(hash_list[:5]):  # 尝试前5个hash值
            # hash_item格式: [file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name]
            file_hash = hash_item[2] if hash_item[2] else hash_item[0]  # 优先使用SQ，然后普通
            if not file_hash:
                continue

            try:
                print(f"尝试获取歌曲信息 (hash {i+1}/{min(5, len(hash_list))}): {file_hash}")
                response = self.kugou.get(self.address + f"/song/url?hash={file_hash}").text
                song_data = json.loads(response)

                # 保存当前尝试的hash信息
                current_hash_info = {
                    'song_name': hash_item[4] if len(hash_item) > 4 else '',
                    'singer_name': hash_item[5] if len(hash_item) > 5 else '',
                    'hash': file_hash
                }
                # 优先获取基本信息，无论状态如何
                if not song_info:
                    song_info = current_hash_info

                # 尝试获取下载URL
                if 'backupUrl' in song_data and song_data['backupUrl']:
                    song_url = song_data['backupUrl'][0]
                    used_hash_info = current_hash_info
                    print(f"✅ 成功获取URL (使用backupUrl)")
                    break
                elif 'url' in song_data and song_data['url']:
                    song_url = song_data['url'][0] if isinstance(song_data['url'], list) else song_data['url']
                    used_hash_info = current_hash_info
                    print(f"✅ 成功获取URL (使用url)")
                    break
                elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
                    song_url = song_data['data']['url']
                    used_hash_info = current_hash_info
                    print(f"✅ 成功获取URL (使用data.url)")
                    break
                else:
                    print(f"⚠️ hash {i+1} 暂无可用URL (status: {song_data.get('status', 'unknown')})")

            except Exception as e:
                print(f"❌ hash {i+1} 请求失败: {e}")
                continue

        if not song_url:
            raise Exception(f"无法获取到有效的音乐URL，已尝试 {min(5, len(hash_list))} 个hash值")

        # 下载音乐文件
        print(f"开始下载音乐文件...")
        song = self.kugou.get(song_url)

        # 生成文件名（包含歌手信息）
        final_info = used_hash_info or song_info
        if final_info:
            song_name = final_info.get('song_name', keywords)
            singer_name = final_info.get('singer_name', '')
        else:
            song_name = keywords
            singer_name = ''

        # 清理文件名中的特殊字符，去除尖括号、引号、冒号、斜杠、问号、星号、句号等特殊字符
        # 左右圆括号也清除掉，左圆括号的地方用下划线代替
        # 去掉空格
        clean_song_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', song_name).rstrip('. ').replace('(', '_').replace(')', '').replace(' ', '')
        clean_singer_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', singer_name).rstrip('. ').replace('(', '_').replace(')', '_').replace(' ', '')

        # 确定文件扩展名
        if song_url.endswith('.mp3'):
            suffix = 'mp3'
        elif song_url.endswith('.flac'):
            suffix = 'flac'
        else:
            suffix = 'mp3'  # 默认mp3

        # 生成包含歌手信息的文件名
        if clean_singer_name:
            filename = f"{clean_singer_name}-{clean_song_name}.{suffix}"
            display_name = f"{clean_singer_name}-{clean_song_name}"
        else:
            filename = f"{clean_song_name}.{suffix}"
            display_name = clean_song_name

        file_path = os.path.join('input', filename)

        with open(file_path, 'wb') as f:
            f.write(song.content)

        print(f"✅ 下载完成: {filename}")
        return display_name, file_path

    def MD5Encrypt(self, text):
        # 返回当前时间的时间戳(1970纪元后经过的浮点秒数)
        k = time.time()
        k = int(round(k * 1000))
        info = ["NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt", "bitrate=0", "callback=callback123",
                "clienttime={}".format(k), "clientver=2000", "dfid=-", "inputtype=0",
                "iscorrection=1", "isfuzzy=0", "keyword={}".format(text), "mid={}".format(k),
                "page=1", "pagesize=30", "platform=WebFilter", "privilege_filter=0",
                "srcappid=2919", "tag=em", "userid=-1", "uuid={}".format(k), "NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt"]
        # 创建md5对象
        new_md5 = md5()
        info = ''.join(info)
        # 更新哈希对象
        new_md5.update(info.encode(encoding='utf-8'))
        # 加密
        signature = new_md5.hexdigest()
        url = 'https://complexsearch.kugou.com/v2/search/song?callback=callback123&keyword={0}' \
              '&page=1&pagesize=30&bitrate=0&isfuzzy=0&tag=em&inputtype=0&platform=WebFilter&userid=-1' \
              '&clientver=2000&iscorrection=1&privilege_filter=0&srcappid=2919&clienttime={1}&' \
              'mid={2}&uuid={3}&dfid=-&signature={4}'.format(parse.quote(text), k, k, k, signature.upper())
        return url

    def get_html(self, url):
        try:
            response = requests.get(url, headers=self.headers, cookies=self.kugou.cookies.get_dict(), verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as err:
            print(err)
            return '请求异常'

    def parse_text(self, text):
        """
        解析搜索结果，提取歌曲信息和hash值
        返回格式: [(file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name), ...]
        """
        hash_list = []
        print('{:*^80}'.format('搜索结果如下'))
        print('{0:{5}<5}{1:{5}<20}{2:{5}<15}{3:{5}<10}{4:{5}<15}'.format('序号', '歌名', '歌手', '时长(s)', '专辑', chr(12288)))
        print('{:-^84}'.format('-'))

        try:
            song_list = json.loads(text)['data']['lists']
        except (json.JSONDecodeError, KeyError) as e:
            print(f"解析搜索结果失败: {e}")
            return hash_list

        for count, song in enumerate(song_list):
            # 清理歌手名称中的HTML标签
            singer_name = song.get('SingerName', '')
            pattern = re.compile(r'<[^>]+>')
            singer_name = re.sub(pattern, '', singer_name)

            # 清理歌曲名称中的HTML标签
            song_name = song.get('SongName', '')
            song_name = re.sub(pattern, '', song_name)

            album_name = song.get('AlbumName', '')
            album_id = song.get('AlbumID', '')
            duration = song.get('Duration', 0)

            # 获取不同音质的hash值
            file_hash = song.get('FileHash', '')
            hq_file_hash = song.get('HQFileHash', '')
            sq_file_hash = song.get('SQFileHash', '')

            # 存储完整信息，包括歌曲名和歌手名
            hash_list.append([file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name])

            print('{0:{5}<5}{1:{5}<20}{2:{5}<15}{3:{5}<10}{4:{5}<15}'.format(
                count, song_name[:18], singer_name[:13], duration, album_name[:13], chr(12288)))

            # 限制显示数量，避免输出过多
            if count >= 29:
                break

        print('{:*^80}'.format('*'))
        return hash_list

    def log_in(self):
        # 略，已有cookie
        pass

    def test_download(self, test_songs=None):
        """
        测试下载功能
        支持格式: ["歌名", "歌手 - 歌名", "歌名 歌手"]
        """
        if test_songs is None:
            test_songs = [
                "蓝光乐队 - 轻舟(dj阿卓版)",
                "草帽酱 - 戏说",
                "张杰 - 轻舟",
                "周杰伦 - 稻香"
            ]

        print(f"开始测试下载功能，共 {len(test_songs)} 首歌曲")
        print("支持搜索格式: '歌名' 或 '歌手 - 歌名'")
        success_count = 0

        for i, song in enumerate(test_songs, 1):
            print(f"\n{'='*60}")
            print(f"[{i}/{len(test_songs)}] 测试歌曲: {song}")
            print(f"{'='*60}")
            try:
                name, file_path = self.download_music(song)
                print(f"✅ 成功下载: {name}")
                print(f"   文件路径: {file_path}")
                success_count += 1
            except Exception as e:
                print(f"❌ 下载失败: {e}")

        print(f"\n{'='*60}")
        print(f"测试完成: {success_count}/{len(test_songs)} 首歌曲下载成功")
        print(f"{'='*60}")
        return success_count == len(test_songs)

if __name__ == '__main__':
    kugou = Kugou_music()

    # 示例用法:
    # 1. 直接下载单首歌曲
    # kugou.download_music("张杰 - 轻舟")
    # kugou.download_music("轻舟(dj阿卓版)")

    # 2. 运行测试（下载多首歌曲）
    kugou.test_download()

    # 3. 自定义测试歌曲列表
    # custom_songs = ["周杰伦 - 稻香", "邓紫棋 - 光年之外", "薛之谦 - 演员"]
    # kugou.test_download(custom_songs)