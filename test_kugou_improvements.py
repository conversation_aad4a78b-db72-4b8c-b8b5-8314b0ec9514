#coding=UTF-8
"""
测试酷狗音乐搜索改进和歌曲存在性检查
"""
import os
import sys

# 添加项目根目录到路径
sys.path.append('.')

from kugou import Kugou_music

def test_kugou_search_improvements():
    """测试酷狗搜索改进功能"""
    print("=" * 60)
    print("测试酷狗音乐搜索改进功能")
    print("=" * 60)

    try:
        kugou = Kugou_music()

        # 测试用例
        test_cases = [
            "张杰 - 轻舟",
            "轻舟",
            "周杰伦 - 稻香",
            "稻香",
            "蓝光乐队 - 轻舟(dj阿卓版)",
            "草帽酱 - 戏说"
        ]

        # 先测试API连接
        print("🔗 测试API连接...")
        try:
            import requests
            response = requests.get("http://localhost:3001/search?keywords=test&limit=1", timeout=3)
            if response.status_code == 200:
                print("✅ API连接正常")
                api_available = True
            else:
                print(f"⚠️ API返回状态码: {response.status_code}")
                api_available = False
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            api_available = False

        if not api_available:
            print("⚠️ 酷狗API服务不可用，跳过在线搜索测试")
            print("💡 请确保酷狗API服务正在运行 (http://localhost:3001)")
            return

        for i, query in enumerate(test_cases, 1):
            print(f"\n[{i}] 测试搜索: {query}")
            print("-" * 40)

            # 测试搜索音乐
            song_id, song_name, artist_name = kugou.search_music(query)
            if song_id != 0:
                print(f"✅ 搜索成功:")
                print(f"   歌曲ID: {song_id}")
                print(f"   歌曲名: {song_name}")
                print(f"   歌手名: {artist_name}")

                # 测试获取文件名
                filename, full_song_name = kugou.get_song_filename(query)
                if filename:
                    print(f"   文件名: {filename}")
                    print(f"   完整名: {full_song_name}")

                    # 检查是否存在对应的文件
                    possible_files = [
                        f"./input/{filename}.mp3",
                        f"./input/{filename}.flac",
                        f"./input/{filename}.wav"
                    ]

                    existing_files = [f for f in possible_files if os.path.exists(f)]
                    if existing_files:
                        print(f"   已存在文件: {existing_files}")
                    else:
                        print(f"   文件不存在，需要下载")
                else:
                    print("❌ 获取文件名失败")
            else:
                print("❌ 搜索失败")

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_match_score_calculation():
    """测试匹配度计算功能"""
    print("\n" + "=" * 60)
    print("测试匹配度计算功能")
    print("=" * 60)

    try:
        kugou = Kugou_music()

        # 测试用例：(搜索词, 歌曲名, 歌手名, 期望匹配度范围)
        test_cases = [
            ("稻香", "稻香", "周杰伦", "高匹配度"),
            ("周杰伦 - 稻香", "稻香", "周杰伦", "最高匹配度"),
            ("轻舟", "轻舟", "张杰", "高匹配度"),
            ("张杰 - 轻舟", "轻舟", "张杰", "最高匹配度"),
            ("轻舟", "轻舟(DJ版)", "蓝光乐队", "中等匹配度"),
            ("蓝光乐队 - 轻舟", "轻舟(DJ版)", "蓝光乐队", "高匹配度"),
            ("不存在的歌", "稻香", "周杰伦", "低匹配度"),
        ]

        for i, (keywords, song_name, artist_name, expected) in enumerate(test_cases, 1):
            score = kugou._calculate_match_score(keywords, song_name, artist_name)
            print(f"[{i}] 搜索: '{keywords}' vs 歌曲: '{song_name}' - '{artist_name}'")
            print(f"    匹配度分数: {score} ({expected})")
            print()

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_file_existence_check():
    """测试文件存在性检查"""
    print("\n" + "=" * 60)
    print("测试文件存在性检查")
    print("=" * 60)
    
    # 检查input目录中的文件
    input_dir = "./input"
    if os.path.exists(input_dir):
        print(f"\n📁 检查 {input_dir} 目录:")
        files = os.listdir(input_dir)
        audio_files = [f for f in files if f.endswith(('.mp3', '.flac', '.wav'))]
        
        if audio_files:
            print(f"   找到 {len(audio_files)} 个音频文件:")
            for file in audio_files[:10]:  # 只显示前10个
                print(f"   - {file}")
            if len(audio_files) > 10:
                print(f"   ... 还有 {len(audio_files) - 10} 个文件")
        else:
            print("   没有找到音频文件")
    else:
        print(f"❌ {input_dir} 目录不存在")
    
    # 检查output目录中的文件
    output_dir = "./output"
    if os.path.exists(output_dir):
        print(f"\n📁 检查 {output_dir} 目录:")
        subdirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
        
        if subdirs:
            print(f"   找到 {len(subdirs)} 个歌曲目录:")
            for subdir in subdirs[:5]:  # 只显示前5个
                subdir_path = os.path.join(output_dir, subdir)
                files = os.listdir(subdir_path)
                audio_files = [f for f in files if f.endswith('.wav')]
                print(f"   - {subdir}: {len(audio_files)} 个wav文件")
            if len(subdirs) > 5:
                print(f"   ... 还有 {len(subdirs) - 5} 个目录")
        else:
            print("   没有找到歌曲目录")
    else:
        print(f"❌ {output_dir} 目录不存在")

def main():
    """主测试函数"""
    print("🎵 酷狗音乐搜索改进测试")
    print("测试时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    # 测试匹配度计算
    test_match_score_calculation()

    # 测试搜索改进
    test_kugou_search_improvements()

    # 测试文件存在性检查
    test_file_existence_check()

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
